use serde::Deserialize;

#[derive(Debug, <PERSON>lone, Deserialize, DeserializeWithPath)]
pub struct HookOptions {
    pub alias: Option<String>,
    pub files: Option<String>,
    pub exclude: Option<String>,
    pub types: Option<Vec<String>>,
    pub types_or: Option<Vec<String>>,
    pub exclude_types: Option<Vec<String>>,
    pub additional_dependencies: Option<Vec<String>>,
    pub args: Option<Vec<String>>,
    pub always_run: Option<bool>,
    pub fail_fast: Option<bool>,
    pub pass_filenames: Option<bool>,
    pub description: Option<String>,
    pub language_version: Option<String>,
    pub log_file: Option<String>,
    pub require_serial: Option<bool>,
    pub stages: Option<Vec<String>>,
    pub verbose: Option<bool>,
    pub minimum_prek_version: Option<String>,
}

#[derive(Debug, <PERSON>lone, Deserialize, DeserializeWithPath)]
pub struct ManifestHook {
    pub id: String,
    pub name: String,
    pub entry: String,
    pub language: String,
    #[serde(flatten)]
    pub options: HookOptions,
}

#[derive(Debug, <PERSON>lone, Deserialize, DeserializeWithPath)]
pub struct Repo {
    pub repo: String,
    pub hooks: Vec<ManifestHook>,
}

#[derive(Debug, Clone, Deserialize, DeserializeWithPath)]
pub struct Config {
    pub repos: Vec<Repo>,
    pub minimum_pre_commit_version: Option<String>,
}

fn main() {
    let yaml = r#"
repos:
  - repo: local
    hooks:
      - id: test-hook1
        name: Test Hook
        entry: echo test
        language: system
        asda:
        unexpected_key_in_hook: some_value
        alias: nihao
      - id: test-hook2
        name: Test Hook2
        entry: echo test2
        language: system
        extra_field: value
unexpected_key: something
another_unknown: 123
minimum_pre_commit_version: 1.0.0
"#;

    let config: Config = Config::deserialize_with_path(&mut serde_yaml::Deserializer::from_str(yaml)).unwrap();

    println!("Config: {:#?}", config);
}
