use proc_macro::TokenStream;
use quote::quote;
use syn::{parse_macro_input, DeriveInput};

#[proc_macro_derive(DeserializeWithPath)]
pub fn derive_deserialize_with_path(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;

    let expanded = quote! {
        impl<'de> #name {
            pub fn deserialize_with_path<D>(deserializer: D) -> Result<Self, D::Error>
            where
                D: serde::Deserializer<'de>,
            {
                let value = serde_yaml::Value::deserialize(deserializer)?;
                let mut unused = Vec::new();
                let result: Self = serde_ignored::deserialize(value, |path| {
                    unused.push(path.to_string());
                }).map_err(serde::de::Error::custom)?;
                if !unused.is_empty() {
                    eprintln!("Unused keys in {}: {:?}", stringify!(#name), unused);
                }
                Ok(result)
            }
        }
    };
    TokenStream::from(expanded)
}
